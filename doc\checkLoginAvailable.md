checkLoginAvailable（一键登录鉴权接口）
 
// 先在服务端调用getAuthToken接口, 获取accessToken, jwtToken
phoneNumberServer.checkLoginAvailable();
checkLoginAvailable 方法参数
 
phoneNumberServer.checkLoginAvailable({
  accessToken: "******",
  jwtToken: "******",
  success: function (res) {
    console.log(res);
    if (res.code === 600000) {
      console.log("鉴权成功", res);
      // 在此调用getLoginToken方法
    }
  },

  error: function (res) {
    console.log("鉴权失败", res);
    // 可提示用户关闭Wi-Fi或者尝试其他登录方案
  }
});
参数名称

参数类型

是否必填

参数说明

accessToken

string

是

业务鉴权Token，由服务端调用GetAuthToken接口获取。有效期10分钟，有效期内可以重复使用，过期时请重新获取。

jwtToken

string

是

API鉴权Token，由服务端调用GetAuthToken接口获取。有效期1小时，有效期内可以重复使用，过期时请重新获取。

timeout

number

否

接口请求超时时间，默认值为10s。

success

function

是

成功回调。入参请参见success/error 回调函数入参。

error

function

是

失败回调。入参请参见success/error 回调函数入参。

success/error 回调函数入参
参数示例

参数名称

参数说明

 
{
  code: "6000xx",
  content: [
    { 
      vender: "CU", 
      result: "100001", 
      resultCode: "101", 
      msgId: "xxxxx"
    }
  ],
  msg: "",
  requestId: ""
}
code

返回的状态码。

返回600000代表请求成功。

其他错误码，请参见错误码。

content

运营商SDK的返回结果，方便客户定位问题。

说明
不同运营商返回的内容消息体存在一些差异，可根据vender字段进行区分处理。

msg

返回结果的描述。

requestId

请求ID，根据此ID可查询相关日志。