.GetPhoneWithToken - 一键登录取号（H5能力专用）
更新时间：2024-12-24 18:04:24
产品详情
我的收藏
本接口用于号码认证服务端（H5能力专用）一键登录取号，成功取得号码后会将号码返回。

接口说明
准备工作
请确保在使用该接口前，已充分了解号码认证服务产品的收费方式和价格。
请先完成阿里云账号注册、获取阿里云访问密钥、创建认证方案等操作。更多信息，请参见快速入门。
说明
本接口仅适用于 H5 场景下的一键登录或注册场景，需最终用户经过一键登录 JS-SDK 提供的授权页确认授权后方可调用。开发者不得通过任何手段模拟或越过授权，否则我方有权终止服务并追究相应法律责任。
QPS 限制
本接口的单用户 QPS 限制为 500 次/秒。超过限制，API 调用会被限流，这可能会影响您的业务，请合理调用。

调试
您可以在OpenAPI Explorer中直接运行该接口，免去您计算签名的困扰。运行成功后，OpenAPI Explorer可以自动生成SDK代码示例。

调试
授权信息
下表是API对应的授权信息，可以在RAM权限策略语句的Action元素中使用，用来给RAM用户或RAM角色授予调用此API的权限。具体说明如下：

操作：是指具体的权限点。
访问级别：是指每个操作的访问级别，取值为写入（Write）、读取（Read）或列出（List）。
资源类型：是指操作中支持授权的资源类型。具体说明如下：
对于必选的资源类型，用背景高亮的方式表示。
对于不支持资源级授权的操作，用全部资源表示。
条件关键字：是指云产品自身定义的条件关键字。
关联操作：是指成功执行操作所需要的其他权限。操作者必须同时具备关联操作的权限，操作才能成功。
操作	访问级别	资源类型	条件关键字	关联操作
dypns:GetPhoneWithToken	get	
*全部资源
*
无
无
请求参数
名称	类型	必填	描述	示例值
SpToken	string	是	
JSSDK 获取的号码认证 Token。 Token 使用有效期：

中国电信 10 分钟，仅可使用一次；
中国联通 30 分钟，仅可使用一次；
中国移动 2 分钟，仅可使用一次。
Dfafdafad542****
返回参数
名称	类型	描述	示例值
object	
结构体

Code	string	
状态码。

返回 OK 代表请求成功。

其他错误码，请参见服务端 API 返回码。

OK
Message	string	
状态码的描述。

请求成功
RequestId	string	
请求 id。

0F335F48-****-****-****-CA7914FE5D77
Data	object	
结构体。

Mobile	string	
手机号。

13900001234
示例
正常返回示例

JSON格式

 
{
  "Code": "OK",
  "Message": "请求成功",
  "RequestId": "0F335F48-****-****-****-CA7914FE5D77",
  "Data": {
    "Mobile": "13900001234"
  }
}
错误码
访问错误中心查看更多错误码。

变更历史
变更时间	变更内