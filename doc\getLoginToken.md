getLoginToken（获取一键登录Token接口）
 
// 在checkLoginAvailable方法的success回调中调用本方法
phoneNumberServer.getLoginToken();
getLoginToken 方法参数
 
phoneNumberServer.getLoginToken({
  authPageOption: {
    navText: "测试",
    subtitle: "", // 副标题
    isHideLogo: false, // logo显示隐藏
    logoImg: "XXX",
    btnText: "立即登录",
    agreeSymbol: "、",
    privacyOne: ["《中国移动认证服务条款》", ""],
    privacyTwo: ["《中国移动认证服务条款》", ""],
    showCustomView: true,
    customView: {
      element:
        '<div class="btn_box other" onclick="clickEvent()">切换其他登录方式</div>',
      style: ".btn_box.other{background: #fff; color: #f00}",
      js: "function clickEvent(){alert(666666)}",
    },
    privacyBefore: "我已阅读并同意",
    privacyEnd: "",
    vendorPrivacyPrefix: "《",
    vendorPrivacySuffix: "》",
    privacyVenderIndex: 2,
    isDialog: true, // 是否是弹窗样式
    manualClose: true, // 是否手动关闭弹窗/授权页
    isPrePageType: false,
    isShowProtocolDesc: false,
    // prePageTypeOption: {
    //     // mount: '',
    //     privacyOne: ['条款1', 'https://wap.cmpassport.com/resources/html/contract.html'],
    //     privacyTwo: ['条款2', 'https://wap.cmpassport.com/resources/html/contract.html'],
    //     // showCustomView: true,
    //     agreeSymbol: '及',
    //     tips: <div style={{ position: 'absolute', top: '10px', right: '10px', borderRadius: '30%', fontSize: '12px', color: '#fff'}}>tips</div>,
    //     btnText: '',
    // }
  },
  success: (res) => {//请至“success/error回调函数入参”区块查看success回调res对象格式示例
    console.log(res);
    if (res.code === 600000) {
      // 拿到spToken去服务端发起Token验证
      phoneNumberServer.closeLoginPage(); // 手动关闭授权页
      res.clearInput(); // 清空输入框并将光标置于第一个输入框
      res.focusOn(2); // 将光标置于第1-4个输入框

      res.setMessage({
        // 设置弹出Toast提示框（有默认样式）
        showMessage: true, // 是否弹出Toast提示框
        messageContent: "test content", // 弹出内容
        messageStyle: {
          // 自定义弹窗样式，写入css样式即可
          color: "#fff",
          borderRadius: "8px",
        },
        time: 3000, // 弹出时间/ms，默认3000毫秒
      });
    }
  },

  error: (res) => {
    // 提示用户关闭Wi-Fi或者尝试其他登录方案
  },

  watch: function (status, data) {
    // console.log('-----------------status', status, data);
    // 当status为2时整个流程结束，比如如果按钮有loading状态此处置为false
  },
});
参数名称

参数类型

是否必填

参数说明

authPageOption

object

是

配置选项。配置详情请参见authPageOption 字段说明。

timeout

number

否

超时时间，单位：秒。建议不低于2秒，默认为3秒。

success

function

是

成功回调。入参请参见success/error 回调函数入参。

error

function

是

失败回调。入参请参见success/error 回调函数入参。

watch

function

否

授权页状态监听函数。入参请参见watch/protocolPageWatch/previewPrivacyWatch 回调函数入参。

protocolPageWatch

function

否

预授权页状态监听函数，isPrePageType参数为true时需填入。入参请参见watch/protocolPageWatch/previewPrivacyWatch 回调函数入参。

previewPrivacyWatch

function

否

协议预览弹窗状态监听函数。入参请参见watch/protocolPageWatch/previewPrivacyWatch 回调函数入参。

privacyAlertWatch

function

否

二次弹窗页面状态监听函数。

success/error 回调函数入参
参数名称

参数类型

参数说明

code

string

返回的状态码。

返回600000代表请求成功。

其他错误码，请参见错误码。

spToken

string

运营商一键登录Token，可在服务端调用GetPhoneWithToken接口进行取号。

clearInput

function

调用清空当前所有输入框，将光标置于第一个输入框。

focusOn

function

入参为：1-4，将光标置于入参对应下标的输入框内。

setMessage

function

设置弹出Toast提示框（有默认样式）。入参格式：

 
{ 
  showMessage: boolean, // 是否弹出Toast提示框 
  messageContent: string, // 弹出内容 
  messageStyle: object, // 自定义弹窗样式 
  time: number // 弹出时间ms
}
vender

string

运营商信息，暂不支持中国广电：

CM 中国移动。

CU 中国联通。

CT 中国电信。

success回调res对象格式示例：

 
{
    "vender": "CM",
    "code": 600000,
    "spToken": "eyJziNmIzYzk0YjQzMjdlOTY4MDk3NjYxY2UzMGI0NTliYjEwNzI5ZmZma2VuIjoiM******",
    "requestId": "cf9ce499-d86e-4b47-95b7-2412cfdc92f3"
}