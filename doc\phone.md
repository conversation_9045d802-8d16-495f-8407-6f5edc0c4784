H5客户端接入
更新时间：2025-07-04 10:38:03
产品详情
我的收藏
本文详细介绍H5页面接入网页端SDK的方式及一键登录相关方法的说明。

说明
在使用过程中如有疑问，可以提交工单联系阿里云技术工程师处理。

接入步骤
创建认证方案
您导入项目或调用API接口时，会用到方案Code等参数信息，请先在号码认证产品控制台，创建认证方案，获取方案Code等参数信息。

资源引入
npm包引入静态资源引入
下载npm资源，并将aliyun_numberauthsdk_web包添加依赖到package.json：

 
npm i aliyun_numberauthsdk_web -S
在脚本中引入：

 
import { PhoneNumberServer } from 'aliyun_numberauthsdk_web'; 
重要
不可在业务代码中覆盖window.PhoneNumberServer变量。

未开启移动数据网络的用户无法通过一键登录完成认证：认证之前确保您的终端设备已关闭Wi-Fi连接且开启了SIM卡的4G移动数据网络（支持中国联通、中国移动的3G网络，但接口耗时会增加）。

交互流程
image..png

初始化并鉴权

用户访问H5页面。

服务端：调用GetAuthToken接口获取业务鉴权accessToken、API鉴权jwtToken。

accessToken有效期10分钟，jwtToken有效期1小时，有效期内可以重复使用，过期时请重新获取。
网页端：调用checkLoginAvailable方法发起身份鉴权。

鉴权成功后进行步骤2。

唤起授权页面

网页端：在鉴权成功的回调内调用getLoginToken方法唤起授权页。

唤起授权页后，授权页会展示待补充完整的号码及运营商协议。

重要
网页端SDK或注册需用户确认授权方可使用，且登录按钮文字描述必须包含“登录”、注册按钮文字描述必须包含“注册”等文字，不可诱导用户授权以及通过任何技术手段跳过或模拟此步骤，否则我方有权停止服务并追究相关法律责任。

阿里云将对接入移动认证SDK并上线的应用授权页面进行审查，若出现未按要求弹出或设计授权页面的，将停止应用的网页端SDK或注册服务。

用户同意授权并登录

用户输入手机号中间四位数字并同意相关协议。

由于在开启数据网络＋WiFi的情况下，JS无法强制浏览器调度使用数据网络取号。如果设备连接他人的共享热点，此时使用WiFi通道取号有概率获取到他人的手机号码。因此H5一键登录时用户需输入手机号中间4位避免安全风险。
单击授权页面的登录或注册按钮，SDK会根据用户输入的掩码返回一键登录spToken。

服务端取号

服务端：调用GetPhoneWithToken接口，换取用户完整手机号。

获取到用户完整手机号后，您可自行实现登录或注册业务逻辑，并将结果返回给H5页面。

H5页面获取到登录状态，关闭授权弹窗。