GetAuthToken - 获取 H5 认证授权 Token
更新时间：2024-12-24 18:04:15
产品详情
我的收藏
获取授权Token用于H5本机号码校验的鉴权，返回结果包括AccessToken和JwtToken。

接口说明
准备工作
请先完成阿里云账号注册、获取阿里云访问密钥、创建认证方案，更多信息，请参见 H5 本机号码校验使用流程。

QPS 限制
本接口的单用户 QPS 限制为 1000 次/秒。超过限制，API 调用会被限流，这可能会影响您的业务，请合理调用。

调试
您可以在OpenAPI Explorer中直接运行该接口，免去您计算签名的困扰。运行成功后，OpenAPI Explorer可以自动生成SDK代码示例。

调试
授权信息
下表是API对应的授权信息，可以在RAM权限策略语句的Action元素中使用，用来给RAM用户或RAM角色授予调用此API的权限。具体说明如下：

操作：是指具体的权限点。
访问级别：是指每个操作的访问级别，取值为写入（Write）、读取（Read）或列出（List）。
资源类型：是指操作中支持授权的资源类型。具体说明如下：
对于必选的资源类型，用背景高亮的方式表示。
对于不支持资源级授权的操作，用全部资源表示。
条件关键字：是指云产品自身定义的条件关键字。
关联操作：是指成功执行操作所需要的其他权限。操作者必须同时具备关联操作的权限，操作才能成功。
操作	访问级别	资源类型	条件关键字	关联操作
dypns:GetAuthToken	create	
*全部资源
*
无
无
请求参数
名称	类型	必填	描述	示例值
Url	string	是	
请求页面地址。请传入在控制台创建认证方案时填入的页面地址，格式为“协议 + // + 域名 + /”。

说明
测试可暂时传入公网 IP 地址，正式上线请使用域名。
https://www.aliyundoc.com/
Origin	string	是	
请求源地址。请传入在控制台创建认证方案时填入的源地址，格式为“协议 + // + 域名”。

https://www.aliyundoc.com
SceneCode	string	否	
方案 Code。

说明
因方案升级，本参数为必填，请在控制台创建认证方案后传入方案 Code，具体操作请参见号码认证方案管理。
FC10000010643****
BizType	integer	否	
业务类型。取值：

0：本机号码认证。
1：一键登录。
说明
因业务流程优化，减少了客户端鉴权接口的调用，本参数为必填。
返回参数
名称	类型	描述	示例值
object	
Code	string	
状态码。

返回 OK 代表请求成功。

其他错误码，请参见服务端 API 返回码。

OK
Message	string	
状态码的描述。

请求成功
RequestId	string	
请求 ID。

8906582E-6722
TokenInfo	object	
结构体。

AccessToken	string	
业务鉴权 Token。

说明
AccessToken 有效期是 10 分钟，有效期内可以重复使用。
agag****
JwtToken	string	
API 鉴权 Token。

说明
JwtToken 有效期是 1 小时，有效期内可以重复使用。
aweghd****
示例
正常返回示例

JSON格式

 
{
  "Code": "OK",
  "Message": "请求成功",
  "RequestId": "8906582E-6722",
  "TokenInfo": {
    "AccessToken": "agag****",
    "JwtToken": "aweghd****"
  }
}
错误码
访问错误中心查看更多错误码。

变更历史
变更时间	变更内容概要	操作
变更时间	变更内容概要	操作
2024-10-24	OpenAPI 入参发生变更	查看变更详情
2023-12-29	OpenAPI 入参发生变更	查看变更详情
2023-06-07	API 内部配置变更，不影响调用	查看变更详情