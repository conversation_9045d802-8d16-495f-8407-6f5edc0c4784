import { eq, or } from 'drizzle-orm';
import { db } from './drizzle';
import { users, type User, type NewUser } from './schema';
import { updateStatistics } from './statistics';
import bcrypt from 'bcryptjs';


// 获取所有用户
export async function getAllUsers(): Promise<User[]> {
	try {
		return await db.select().from(users);
	} catch (error) {
		console.error("Error getting all users:", error);
		throw error;
	}
}


// 更新用户信息
export async function updateUser(
	dingTalkUnionId: string,
	updates: Partial<Omit<User, 'id' | 'dingTalkUnionId' | 'createdAt'>>
): Promise<User[]> {
	try {
		const now = new Date().toISOString();
		return await db
			.update(users)
			.set({ ...updates, updatedAt: now })
			.where(eq(users.dingTalkUnionId, dingTalkUnionId))
			.returning();
	} catch (error) {
		console.error("Error updating user:", error);
		throw error;
	}
}

// 删除用户
export async function deleteUser(dingTalkUnionId: string): Promise<void> {
	try {
		await db.delete(users).where(eq(users.dingTalkUnionId, dingTalkUnionId));
	} catch (error) {
		console.error("Error deleting user:", error);
		throw error;
	}
}

// 根据钉钉UnionId获取用户
export async function getUserByDingTalkUnionId(dingTalkUnionId: string): Promise<User | null> {
	try {
		const result = await db.select().from(users).where(eq(users.dingTalkUnionId, dingTalkUnionId));
		return result.length > 0 ? result[0] : null;
	} catch (error) {
		console.error("Error getting user by DingTalk UnionId:", error);
		throw error;
	}
}

// 创建用户（包含完整的钉钉信息）
export async function createUserWithDingTalkInfo(
	dingTalkInfo: {
		dingTalkUnionId: string; // 必须提供UnionId
		dingTalkUserId?: string;
		name?: string;
		avatar?: string;
		mobile?: string;
	}
): Promise<User> {
	try {
		const newUser: NewUser = {
			dingTalkUnionId: dingTalkInfo.dingTalkUnionId,
			dingTalkUserId: dingTalkInfo.dingTalkUserId,
			name: dingTalkInfo.name || '钉钉用户',
			avatar: dingTalkInfo.avatar,
			mobile: dingTalkInfo.mobile,
			isAdmin: false, // 默认非管理员
			token: 0, // 默认 token
			requestTimes: 0, // 默认请求次数
		};
		const result = await db.insert(users).values(newUser).returning();
		return result[0];
	} catch (error) {
		console.error("Error creating user with DingTalk info:", error);
		throw error;
	}
}

// 获取或创建用户
export async function getOrCreateUser(dingTalkUnionId: string): Promise<User> {
	// 先尝试获取用户
	let user = await getUserByDingTalkUnionId(dingTalkUnionId);

	if (!user) {
		// 用户不存在，创建新用户
		const dingTalkInfo = {
			dingTalkUnionId,
			name: '新用户',
			mobile: '未设置',
		};

		user = await createUserWithDingTalkInfo(dingTalkInfo);
		console.log("创建新用户:", user);
	} else {
		console.log("用户已存在:", user);
	}

	return user;
}

// 根据邮箱获取用户
export async function getUserByEmail(email: string): Promise<User | null> {
	try {
		const result = await db.select().from(users).where(eq(users.email, email));
		return result.length > 0 ? result[0] : null;
	} catch (error) {
		console.error("Error getting user by email:", error);
		throw error;
	}
}

// 根据用户ID获取用户
export async function getUserById(id: number): Promise<User | null> {
	try {
		const result = await db.select().from(users).where(eq(users.id, id));
		return result.length > 0 ? result[0] : null;
	} catch (error) {
		console.error("Error getting user by id:", error);
		throw error;
	}
}

// 创建邮箱用户
export async function createUserWithEmail(
	userData: {
		email: string;
		password: string;
		name?: string;
	}
): Promise<User> {
	try {
		// 检查邮箱是否已存在
		const existingUser = await getUserByEmail(userData.email);
		if (existingUser) {
			throw new Error('Email already exists');
		}

		// 加密密码
		const saltRounds = 10;
		const hashedPassword = await bcrypt.hash(userData.password, saltRounds);

		const newUser: NewUser = {
			email: userData.email,
			password: hashedPassword,
			name: userData.name || '新用户',
			isAdmin: false,
			token: 0,
			requestTimes: 0,
		};

		const result = await db.insert(users).values(newUser).returning();
		return result[0];
	} catch (error) {
		console.error("Error creating user with email:", error);
		throw error;
	}
}

// 验证用户密码
export async function verifyUserPassword(email: string, password: string): Promise<User | null> {
	try {
		const user = await getUserByEmail(email);
		if (!user || !user.password) {
			return null;
		}

		const isPasswordValid = await bcrypt.compare(password, user.password);
		if (!isPasswordValid) {
			return null;
		}

		return user;
	} catch (error) {
		console.error("Error verifying user password:", error);
		throw error;
	}
}

// 根据邮箱或钉钉UnionId获取用户
export async function getUserByEmailOrDingTalk(identifier: string): Promise<User | null> {
	try {
		const result = await db.select().from(users).where(
			or(
				eq(users.email, identifier),
				eq(users.dingTalkUnionId, identifier)
			)
		);
		return result.length > 0 ? result[0] : null;
	} catch (error) {
		console.error("Error getting user by email or DingTalk:", error);
		throw error;
	}
}



// 记录用户token使用量和请求次数（通过用户ID）
export async function updateUserTokenUsageById(
	userId: number,
	tokenUsed: number
): Promise<User | null> {
	try {
		// 先获取当前用户信息
		const currentUser = await getUserById(userId);
		if (!currentUser) {
			console.error("用户不存在:", userId);
			return null;
		}

		// 计算新的token累计使用量和请求次数
		const newTokenUsage = currentUser.token + tokenUsed;
		const newRequestTimes = currentUser.requestTimes + 1;

		// 更新用户数据库
		const result = await db
			.update(users)
			.set({
				token: newTokenUsage,
				requestTimes: newRequestTimes,
				updatedAt: new Date().toISOString()
			})
			.where(eq(users.id, userId))
			.returning();

		// 同时更新统计表
		await updateStatistics(1, tokenUsed);

		console.log(`用户 ${userId} token使用情况更新:`, {
			tokenUsed,
			oldUsage: currentUser.token,
			newUsage: newTokenUsage,
			requestTimes: newRequestTimes
		});

		return result.length > 0 ? result[0] : null;
	} catch (error) {
		console.error("Error updating user token usage by ID:", error);
		throw error;
	}
}

// 记录用户token使用量和请求次数（通过钉钉UnionId，保持向后兼容）
export async function updateUserTokenUsage(
	dingTalkUnionId: string,
	tokenUsed: number
): Promise<User | null> {
	try {
		// 先获取当前用户信息
		const currentUser = await getUserByDingTalkUnionId(dingTalkUnionId);
		if (!currentUser) {
			console.error("用户不存在:", dingTalkUnionId);
			return null;
		}

		// 使用用户ID更新
		return await updateUserTokenUsageById(currentUser.id, tokenUsed);
	} catch (error) {
		console.error("Error updating user token usage:", error);
		throw error;
	}
}

